'use client';

import React, { useState, useEffect } from 'react';
import { motion, useScroll, useTransform } from 'framer-motion';
import { ArrowRight, Download, Github, Linkedin, Mail } from 'lucide-react';
import { Button } from '@/components/ui';
import { TypewriterText, AnimatedWords, GradientText } from '@/components/animations/TypewriterText';
import { ScrollIndicator } from '@/components/animations/ScrollIndicator';
import { ParticleSystem, FloatingShapes, AnimatedGrid } from '@/components/animations/ParticleSystem';
import { useSmoothScroll } from '@/hooks/useScrollAnimation';
import { PERSONAL_INFO, SOCIAL_LINKS } from '@/lib/constants';
import { cn } from '@/lib/utils';

// Dynamic icon mapping for social links
const iconMap: Record<string, React.ComponentType<{ size?: number; className?: string }>> = {
  Gith<PERSON>,
  Linkedin,
  Mail,
};

export const Hero: React.FC = () => {
  const [isLoaded, setIsLoaded] = useState(false);
  const { scrollToElement } = useSmoothScroll();
  const { scrollY } = useScroll();
  
  // Parallax effects
  const backgroundY = useTransform(scrollY, [0, 1000], [0, -300]);
  const textY = useTransform(scrollY, [0, 1000], [0, -150]);
  
  // Typewriter texts for dynamic effect
  const typewriterTexts = [
    'Full-Stack Developer',
    'React Specialist',
    'TypeScript Expert',
    'Cal Poly Graduate',
    'Problem Solver',
  ];

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  const handleCTAClick = () => {
    scrollToElement('projects');
  };

  const handleContactClick = () => {
    scrollToElement('contact');
  };

  return (
    <section 
      id="home" 
      className="relative min-h-screen flex items-center justify-center overflow-hidden"
    >
      {/* Animated Background */}
      <motion.div
        className="absolute inset-0 z-0"
        style={{ y: backgroundY }}
      >
        {/* Gradient Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900 via-black to-gray-800" />

        {/* Animated Grid */}
        <AnimatedGrid gridSize={60} opacity={0.02} />

        {/* Particle System */}
        <ParticleSystem
          particleCount={40}
          color="rgba(255, 255, 255, 0.15)"
          speed={0.3}
        />

        {/* Floating Shapes */}
        <FloatingShapes />
      </motion.div>

      {/* Main Content */}
      <motion.div
        className="relative z-10 container mx-auto px-4 sm:px-6 lg:px-8 text-center"
        style={{ y: textY }}
      >
        <div className="max-w-4xl mx-auto">
          {/* Greeting */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mb-6"
          >
            <span className="inline-block px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white/80 text-sm font-medium border border-white/20">
              👋 Hello, I'm
            </span>
          </motion.div>

          {/* Name */}
          <motion.h1
            className="text-5xl md:text-7xl lg:text-8xl font-bold mb-6"
            initial={{ opacity: 0, y: 40 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.8, delay: 0.5 }}
          >
            <GradientText
              gradient="from-white via-white to-white/80"
              className="block"
            >
              {PERSONAL_INFO.name}
            </GradientText>
          </motion.h1>

          {/* Dynamic Title */}
          <motion.div
            className="text-2xl md:text-4xl lg:text-5xl font-semibold mb-8 h-16 flex items-center justify-center"
            initial={{ opacity: 0, y: 30 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 0.8 }}
          >
            <motion.span
              className="text-white/90 mr-4"
              initial={{ opacity: 0 }}
              animate={isLoaded ? { opacity: 1 } : {}}
              transition={{ duration: 0.4, delay: 1.1 }}
            >
              I'm a
            </motion.span>
            <motion.div
              initial={{ opacity: 0 }}
              animate={isLoaded ? { opacity: 1 } : {}}
              transition={{ duration: 0.4, delay: 1.3 }}
            >
              <TypewriterText
                texts={typewriterTexts}
                speed={100}
                delay={2000}
                className="text-white font-bold"
              />
            </motion.div>
          </motion.div>

          {/* Description */}
          <motion.p
            className="text-lg md:text-xl text-white/70 mb-12 max-w-2xl mx-auto leading-relaxed"
            initial={{ opacity: 0, y: 30 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 1.1 }}
          >
            {PERSONAL_INFO.bio}
          </motion.p>

          {/* CTA Buttons */}
          <motion.div
            className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 1.4 }}
          >
            <Button
              variant="primary"
              size="lg"
              onClick={handleCTAClick}
              className="bg-white text-black hover:bg-white/90 font-semibold group"
            >
              View My Work
              <ArrowRight size={20} className="ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
            
            <Button
              variant="secondary"
              size="lg"
              onClick={handleContactClick}
              className="border-white/30 text-white hover:bg-white/10 hover:border-white/50"
            >
              Get In Touch
            </Button>

            {PERSONAL_INFO.resume && (
              <Button
                variant="ghost"
                size="lg"
                href={PERSONAL_INFO.resume}
                className="text-white/80 hover:text-white hover:bg-white/5"
              >
                <Download size={20} className="mr-2" />
                Resume
              </Button>
            )}
          </motion.div>

          {/* Social Links */}
          <motion.div
            className="flex justify-center gap-6 mb-16"
            initial={{ opacity: 0, y: 30 }}
            animate={isLoaded ? { opacity: 1, y: 0 } : {}}
            transition={{ duration: 0.6, delay: 1.7 }}
          >
            {SOCIAL_LINKS.slice(0, 3).map((social, index) => {
              const IconComponent = iconMap[social.icon];

              return (
                <motion.a
                  key={social.platform}
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-3 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-white/70 hover:text-white hover:bg-white/20 transition-all duration-300"
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20, scale: 0.8 }}
                  animate={isLoaded ? { opacity: 1, y: 0, scale: 1 } : {}}
                  transition={{
                    duration: 0.4,
                    delay: 1.9 + index * 0.1,
                    type: "spring",
                    stiffness: 200,
                    damping: 20
                  }}
                  aria-label={social.label}
                >
                  {IconComponent && <IconComponent size={24} />}
                </motion.a>
              );
            })}
          </motion.div>
        </div>
      </motion.div>

      {/* Scroll Indicator */}
      <motion.div
        className="absolute bottom-8 left-0 right-0 flex justify-center items-center"
        initial={{ opacity: 0, y: 20 }}
        animate={isLoaded ? { opacity: 1, y: 0 } : {}}
        transition={{ delay: 2.2, duration: 0.6 }}
      >
        <ScrollIndicator targetSection="about" variant="mouse" />
      </motion.div>

      {/* Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          className="absolute top-20 left-10 w-2 h-2 bg-white/30 rounded-full"
          initial={{ opacity: 0, scale: 0 }}
          animate={isLoaded ? {
            opacity: [0, 0.3, 0.8, 0.3],
            scale: [0, 1, 1.5, 1],
          } : {}}
          transition={{
            duration: 3,
            repeat: Infinity,
            delay: 2.5,
          }}
        />
        <motion.div
          className="absolute bottom-32 right-16 w-1 h-1 bg-white/40 rounded-full"
          initial={{ opacity: 0, scale: 0 }}
          animate={isLoaded ? {
            opacity: [0, 0.4, 1, 0.4],
            scale: [0, 1, 2, 1],
          } : {}}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 2.8,
          }}
        />
      </div>
    </section>
  );
};

export default Hero;
