'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Header } from './Header';
import { Footer } from './Footer';
import ScrollProgress from '@/components/animations/ScrollProgress';
import { pageTransition } from '@/lib/animations';

interface MainLayoutProps {
  children: React.ReactNode;
  className?: string;
}

export const MainLayout: React.FC<MainLayoutProps> = ({ 
  children, 
  className = '' 
}) => {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Scroll Progress Indicator */}
      <ScrollProgress />
      
      {/* Header */}
      <Header />
      
      {/* Main Content */}
      <motion.main
        className={`flex-1 ${className}`}
        variants={pageTransition}
        initial="initial"
        animate="animate"
        exit="exit"
      >
        <AnimatePresence mode="wait">
          {children}
        </AnimatePresence>
      </motion.main>
      
      {/* Footer */}
      <Footer />
    </div>
  );
};

export default MainLayout;
