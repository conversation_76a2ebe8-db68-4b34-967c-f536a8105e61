'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Menu, X } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useScrollTrigger, useSmoothScroll } from '@/hooks/useScrollAnimation';
import { Button, IconButton } from '@/components/ui';
import { PERSONAL_INFO, NAV_ITEMS } from '@/lib/constants';
import { navSlideDown } from '@/lib/animations';

export const Header: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [activeSection, setActiveSection] = useState('home');
  const isScrolled = useScrollTrigger(50);
  const { scrollToElement } = useSmoothScroll();

  // Close mobile menu when clicking outside or on link
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 768) {
        setIsMobileMenuOpen(false);
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Track active section based on scroll position
  useEffect(() => {
    const handleScroll = () => {
      const sections = NAV_ITEMS.map(item => item.href.replace('#', ''));
      const scrollPosition = window.scrollY + 100;

      for (const section of sections) {
        const element = document.getElementById(section);
        if (element) {
          const { offsetTop, offsetHeight } = element;
          if (scrollPosition >= offsetTop && scrollPosition < offsetTop + offsetHeight) {
            setActiveSection(section);
            break;
          }
        }
      }
    };

    window.addEventListener('scroll', handleScroll, { passive: true });
    handleScroll(); // Initial check

    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (href: string) => {
    const sectionId = href.replace('#', '');
    scrollToElement(sectionId);
    setIsMobileMenuOpen(false);
  };

  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <>
      <motion.header
        className={cn(
          'fixed top-0 left-0 right-0 z-50 transition-all duration-300',
          isScrolled
            ? 'bg-background/80 backdrop-blur-md border-b border-border shadow-sm'
            : 'bg-transparent'
        )}
        variants={navSlideDown}
        initial="hidden"
        animate="visible"
      >
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16 md:h-20">
            {/* Logo */}
            <motion.div
              className="flex-shrink-0"
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <button
                onClick={() => handleNavClick('#home')}
                className="text-xl md:text-2xl font-bold text-foreground hover:text-primary transition-colors"
              >
                {PERSONAL_INFO.name.split(' ').map((name, index) => (
                  <span key={index} className={index === 0 ? 'text-primary' : ''}>
                    {name}
                    {index === 0 && ' '}
                  </span>
                ))}
              </button>
            </motion.div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-8">
              {NAV_ITEMS.map((item, index) => {
                const sectionId = item.href.replace('#', '');
                const isActive = activeSection === sectionId;

                return (
                  <motion.button
                    key={item.href}
                    onClick={() => handleNavClick(item.href)}
                    className={cn(
                      'relative px-3 py-2 text-sm font-medium transition-colors',
                      isActive
                        ? 'text-primary'
                        : 'text-muted-foreground hover:text-foreground'
                    )}
                    whileHover={{ y: -2 }}
                    whileTap={{ y: 0 }}
                    initial={{ opacity: 0, y: -20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    {item.label}
                    
                    {/* Active indicator */}
                    {isActive && (
                      <motion.div
                        className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"
                        layoutId="activeIndicator"
                        initial={{ scaleX: 0 }}
                        animate={{ scaleX: 1 }}
                        transition={{ duration: 0.3 }}
                      />
                    )}
                  </motion.button>
                );
              })}
            </nav>

            {/* CTA Button (Desktop) */}
            <div className="hidden md:flex items-center space-x-4">
              <Button
                variant="primary"
                size="sm"
                onClick={() => handleNavClick('#contact')}
              >
                Get In Touch
              </Button>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <IconButton
                variant="ghost"
                size="sm"
                onClick={toggleMobileMenu}
                icon={isMobileMenuOpen ? <X size={20} /> : <Menu size={20} />}
                aria-label="Toggle mobile menu"
              />
            </div>
          </div>
        </div>
      </motion.header>

      {/* Mobile Menu Overlay */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <>
            {/* Backdrop */}
            <motion.div
              className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMobileMenuOpen(false)}
            />

            {/* Mobile Menu */}
            <motion.div
              className="fixed top-16 right-0 bottom-0 z-50 w-64 bg-background border-l border-border md:hidden"
              initial={{ x: '100%' }}
              animate={{ x: 0 }}
              exit={{ x: '100%' }}
              transition={{ type: 'tween', duration: 0.3 }}
            >
              <div className="flex flex-col h-full">
                {/* Navigation Links */}
                <nav className="flex-1 px-6 py-8 space-y-6">
                  {NAV_ITEMS.map((item, index) => {
                    const sectionId = item.href.replace('#', '');
                    const isActive = activeSection === sectionId;

                    return (
                      <motion.button
                        key={item.href}
                        onClick={() => handleNavClick(item.href)}
                        className={cn(
                          'block w-full text-left px-3 py-2 text-lg font-medium transition-colors',
                          isActive
                            ? 'text-primary bg-accent rounded-lg'
                            : 'text-muted-foreground hover:text-foreground'
                        )}
                        initial={{ opacity: 0, x: 20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.1 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {item.label}
                      </motion.button>
                    );
                  })}
                </nav>

                {/* Mobile CTA */}
                <div className="px-6 py-6 border-t border-border">
                  <Button
                    variant="primary"
                    className="w-full"
                    onClick={() => handleNavClick('#contact')}
                  >
                    Get In Touch
                  </Button>
                </div>
              </div>
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Spacer to prevent content from hiding behind fixed header */}
      <div className="h-16 md:h-20" />
    </>
  );
};

export default Header;
