'use client';

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Monitor, Server, Database, Palette, ChevronRight } from 'lucide-react';
import { Card, CardContent, SkillCard } from '@/components/ui';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { SKILLS } from '@/lib/constants';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

// Icon mapping for skill categories
const categoryIcons = {
  'Frontend Development': Monitor,
  'Backend Development': Server,
  'Database & Tools': Database,
  'Design & Animation': Palette,
};

export const Skills: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation({ threshold: 0.1 });
  const [activeCategory, setActiveCategory] = useState(0);
  const [hoveredSkill, setHoveredSkill] = useState<string | null>(null);

  return (
    <section 
      id="skills" 
      ref={ref}
      className="py-20 lg:py-32 bg-muted/20"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div 
            variants={staggerItem}
            className="text-center mb-16"
          >
            <motion.h2 
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6"
              variants={fadeInUp}
            >
              Skills & Expertise
            </motion.h2>
            <motion.p 
              className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              variants={fadeInUp}
            >
              A comprehensive toolkit built through years of learning, 
              practice, and real-world application.
            </motion.p>
          </motion.div>

          {/* Category Navigation */}
          <motion.div 
            variants={staggerItem}
            className="flex flex-wrap justify-center gap-4 mb-12"
          >
            {SKILLS.map((category, index) => {
              const IconComponent = categoryIcons[category.name as keyof typeof categoryIcons];
              const isActive = activeCategory === index;
              
              return (
                <motion.button
                  key={category.name}
                  onClick={() => setActiveCategory(index)}
                  className={`
                    flex items-center gap-3 px-6 py-3 rounded-lg font-medium transition-all duration-300
                    ${isActive 
                      ? 'bg-primary text-white shadow-lg' 
                      : 'bg-background text-muted-foreground hover:text-foreground hover:bg-accent'
                    }
                  `}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {IconComponent && (
                    <IconComponent size={20} />
                  )}
                  <span className="hidden sm:inline">{category.name}</span>
                  <span className="sm:hidden">{category.name.split(' ')[0]}</span>
                </motion.button>
              );
            })}
          </motion.div>

          {/* Skills Grid */}
          <motion.div
            key={activeCategory}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3 }}
            className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16"
          >
              {SKILLS[activeCategory].skills.map((skill, index) => (
                <motion.div
                  key={skill.name}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  onHoverStart={() => setHoveredSkill(skill.name)}
                  onHoverEnd={() => setHoveredSkill(null)}
                >
                  <Card className="p-6 h-full hover:shadow-lg transition-all duration-300 group">
                    <CardContent className="text-center space-y-4">
                      {/* Skill Icon/Color */}
                      <div 
                        className="w-12 h-12 mx-auto rounded-lg flex items-center justify-center text-white font-bold text-lg group-hover:scale-110 transition-transform duration-300"
                        style={{ backgroundColor: skill.color || '#3b82f6' }}
                      >
                        {skill.name.charAt(0)}
                      </div>
                      
                      {/* Skill Name */}
                      <h4 className="text-lg font-semibold text-foreground">
                        {skill.name}
                      </h4>
                      
                      {/* Progress Bar */}
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="text-muted-foreground">Proficiency</span>
                          <span className="text-foreground font-medium">{skill.level}%</span>
                        </div>
                        <div className="h-2 bg-muted rounded-full overflow-hidden">
                          <motion.div
                            className="h-full rounded-full"
                            style={{ backgroundColor: skill.color || '#3b82f6' }}
                            initial={{ width: 0 }}
                            animate={isVisible ? { width: `${skill.level}%` } : { width: 0 }}
                            transition={{ 
                              duration: 1, 
                              delay: 0.5 + index * 0.1,
                              ease: 'easeOut'
                            }}
                          />
                        </div>
                      </div>
                      
                      {/* Years of Experience */}
                      {skill.yearsOfExperience && (
                        <div className="text-sm text-muted-foreground">
                          {skill.yearsOfExperience} year{skill.yearsOfExperience !== 1 ? 's' : ''} experience
                        </div>
                      )}
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </motion.div>

          {/* Skill Details Panel */}
          <AnimatePresence>
            {hoveredSkill && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="bg-accent/50 rounded-lg p-6 mb-8"
              >
                {(() => {
                  const skill = SKILLS[activeCategory].skills.find(s => s.name === hoveredSkill);
                  if (!skill) return null;
                  
                  return (
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="text-lg font-semibold text-foreground mb-2">
                          {skill.name}
                        </h4>
                        <p className="text-muted-foreground">
                          {skill.yearsOfExperience} years of experience • {skill.level}% proficiency
                        </p>
                      </div>
                      <div 
                        className="w-16 h-16 rounded-lg flex items-center justify-center text-white font-bold text-xl"
                        style={{ backgroundColor: skill.color || '#3b82f6' }}
                      >
                        {skill.name.charAt(0)}
                      </div>
                    </div>
                  );
                })()}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Call to Action */}
          <motion.div 
            variants={staggerItem}
            className="text-center"
          >
            <motion.div
              variants={fadeInUp}
              className="bg-gradient-to-r from-primary/10 to-primary/5 rounded-2xl p-8 border border-primary/20"
            >
              <h3 className="text-2xl font-semibold text-foreground mb-4">
                Ready to Work Together?
              </h3>
              <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
                I'm always excited to take on new challenges and collaborate on 
                innovative projects. Let's build something amazing together.
              </p>
              <motion.button
                className="inline-flex items-center gap-2 bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors group"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => {
                  document.getElementById('contact')?.scrollIntoView({ 
                    behavior: 'smooth' 
                  });
                }}
              >
                Let's Connect
                <ChevronRight size={20} className="group-hover:translate-x-1 transition-transform" />
              </motion.button>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default Skills;
