'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ChevronDown, Mouse } from 'lucide-react';
import { useSmoothScroll } from '@/hooks/useScrollAnimation';

interface ScrollIndicatorProps {
  targetSection?: string;
  className?: string;
  variant?: 'arrow' | 'mouse' | 'dots';
  animated?: boolean;
}

export const ScrollIndicator: React.FC<ScrollIndicatorProps> = ({
  targetSection = 'about',
  className = '',
  variant = 'mouse',
  animated = true,
}) => {
  const { scrollToElement } = useSmoothScroll();

  const handleClick = () => {
    scrollToElement(targetSection);
  };

  const bounceAnimation = {
    y: [0, 8, 0],
    transition: {
      duration: 2,
      repeat: Infinity,
      ease: 'easeInOut',
    },
  };

  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.6, delay: 2 },
  };

  if (variant === 'arrow') {
    return (
      <motion.button
        onClick={handleClick}
        className={`flex flex-col items-center gap-2 text-white/70 hover:text-white transition-colors group ${className}`}
        {...fadeInUp}
        animate={animated ? bounceAnimation : undefined}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <span className="text-sm font-medium">Scroll Down</span>
        <ChevronDown size={24} className="group-hover:text-white" />
      </motion.button>
    );
  }

  if (variant === 'dots') {
    return (
      <motion.button
        onClick={handleClick}
        className={`flex flex-col items-center gap-2 ${className}`}
        {...fadeInUp}
        animate={animated ? bounceAnimation : undefined}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.95 }}
      >
        <div className="flex flex-col gap-1">
          {[0, 1, 2].map((index) => (
            <motion.div
              key={index}
              className="w-1 h-1 bg-white/50 rounded-full"
              animate={animated ? {
                opacity: [0.3, 1, 0.3],
                scale: [1, 1.2, 1],
              } : undefined}
              transition={animated ? {
                duration: 1.5,
                repeat: Infinity,
                delay: index * 0.2,
              } : undefined}
            />
          ))}
        </div>
      </motion.button>
    );
  }

  // Default mouse variant
  return (
    <motion.button
      onClick={handleClick}
      className={`flex flex-col items-center gap-3 text-white/70 hover:text-white transition-colors group ${className}`}
      {...fadeInUp}
      whileHover={{ scale: 1.05 }}
      whileTap={{ scale: 0.95 }}
    >
      <span className="text-sm font-medium">Scroll to explore</span>
      <div className="relative">
        {/* Mouse outline */}
        <motion.div
          className="w-6 h-10 border-2 border-white/50 rounded-full relative group-hover:border-white/70"
          animate={animated ? bounceAnimation : undefined}
        >
          {/* Mouse wheel */}
          <motion.div
            className="w-1 h-2 bg-white/50 rounded-full absolute left-1/2 top-2 transform -translate-x-1/2 group-hover:bg-white/70"
            animate={animated ? {
              y: [0, 4, 0],
              opacity: [1, 0.3, 1],
            } : undefined}
            transition={animated ? {
              duration: 1.5,
              repeat: Infinity,
              ease: 'easeInOut',
            } : undefined}
          />
        </motion.div>
      </div>
    </motion.button>
  );
};

// Floating scroll hint
export const FloatingScrollHint: React.FC<{
  className?: string;
}> = ({ className = '' }) => {
  return (
    <motion.div
      className={`fixed bottom-8 left-1/2 transform -translate-x-1/2 z-40 ${className}`}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, delay: 3 }}
    >
      <ScrollIndicator variant="mouse" />
    </motion.div>
  );
};

// Scroll progress dots
export const ScrollProgressDots: React.FC<{
  sections: string[];
  activeSection: string;
  className?: string;
}> = ({ sections, activeSection, className = '' }) => {
  const { scrollToElement } = useSmoothScroll();

  return (
    <motion.div
      className={`fixed right-8 top-1/2 transform -translate-y-1/2 z-40 flex flex-col gap-4 ${className}`}
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{ duration: 0.6, delay: 1 }}
    >
      {sections.map((section, index) => {
        const isActive = activeSection === section;
        
        return (
          <motion.button
            key={section}
            onClick={() => scrollToElement(section)}
            className="group relative"
            whileHover={{ scale: 1.2 }}
            whileTap={{ scale: 0.9 }}
          >
            <motion.div
              className={`w-3 h-3 rounded-full border-2 transition-all duration-300 ${
                isActive 
                  ? 'bg-white border-white' 
                  : 'bg-transparent border-white/50 group-hover:border-white/70'
              }`}
              layoutId="activeDot"
            />
            
            {/* Tooltip */}
            <motion.div
              className="absolute right-6 top-1/2 transform -translate-y-1/2 bg-black/80 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap"
              initial={{ x: 10 }}
              whileHover={{ x: 0 }}
            >
              {section.charAt(0).toUpperCase() + section.slice(1)}
            </motion.div>
          </motion.button>
        );
      })}
    </motion.div>
  );
};

export default ScrollIndicator;
