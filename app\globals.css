@import "tailwindcss";

/* Design System Variables */
:root {
  /* Colors */
  --background: #ffffff;
  --foreground: #0a0a0a;
  --muted: #f5f5f5;
  --muted-foreground: #737373;
  --border: #e5e5e5;
  --accent: #f5f5f5;
  --accent-foreground: #0a0a0a;
  --primary: #18181b;
  --secondary: #71717a;
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Animation Variables */
  --ease-out: cubic-bezier(0.16, 1, 0.3, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-spring: cubic-bezier(0.34, 1.56, 0.64, 1);
  --duration-fast: 150ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 700ms;
}

@theme inline {
  /* Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-border: var(--border);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-primary: var(--primary);
  --color-secondary: var(--secondary);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);

  /* Typography */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Animation Timing Functions */
  --ease-out: var(--ease-out);
  --ease-in-out: var(--ease-in-out);
  --ease-spring: var(--ease-spring);

  /* Animation Durations */
  --animate-fast: var(--duration-fast);
  --animate-normal: var(--duration-normal);
  --animate-slow: var(--duration-slow);
  --animate-slower: var(--duration-slower);

  /* Custom Animations */
  --animate-fade-in-up: fade-in-up var(--duration-slow) var(--ease-out);
  --animate-slide-in: slide-in var(--duration-normal) var(--ease-out);
  --animate-scale-in: scale-in var(--duration-normal) var(--ease-spring);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #fafafa;
    --muted: #262626;
    --muted-foreground: #a3a3a3;
    --border: #262626;
    --accent: #262626;
    --accent-foreground: #fafafa;
  }
}

/* Global Styles */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Animations */
@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Smooth Scrolling Enhancement */
@media (prefers-reduced-motion: no-preference) {
  html {
    scroll-behavior: smooth;
  }

  * {
    transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
    transition-timing-function: var(--ease-out);
    transition-duration: var(--duration-fast);
  }
}

/* Respect reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}
