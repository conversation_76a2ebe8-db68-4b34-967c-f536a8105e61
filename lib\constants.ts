import { NavItem, SocialLink, Project, Experience, SkillCategory } from './types';

// Personal Information
export const PERSONAL_INFO = {
  name: '<PERSON>',
  title: 'Full-Stack Developer',
  subtitle: 'Computer Science Graduate from Cal Poly California',
  email: '<EMAIL>', 
  location: 'California, USA',
  bio: 'Passionate full-stack developer with a Computer Science degree from California Polytechnic University, Pomona. I create modern web applications with a focus on user experience and clean, maintainable code.',
  resume: '/documents/tyler-hipolito-resume.pdf', // Add when available
} as const;

// Navigation Items
export const NAV_ITEMS: NavItem[] = [
  { label: 'Home', href: '#home' },
  { label: 'About', href: '#about' },
  { label: 'Projects', href: '#projects' },
  { label: 'Experience', href: '#experience' },
  { label: 'Contact', href: '#contact' },
];

// Social Media Links
export const SOCIAL_LINKS: SocialLink[] = [
  {
    platform: 'GitHub',
    url: 'https://github.com/tylerhipolito', // Update with real GitHub
    icon: 'Github',
    label: 'View my code on GitHub',
  },
  {
    platform: 'LinkedIn',
    url: 'https://linkedin.com/in/tylerhipolito', // Update with real LinkedIn
    icon: 'Linkedin',
    label: 'Connect with me on LinkedIn',
  },
  {
    platform: 'Email',
    url: 'mailto:<EMAIL>', // Update with real email
    icon: 'Mail',
    label: 'Send me an email',
  },
  {
    platform: 'Twitter',
    url: 'https://twitter.com/tylerhipolito', // Update with real Twitter
    icon: 'Twitter',
    label: 'Follow me on Twitter',
  },
];

// Projects Data
export const PROJECTS: Project[] = [
  {
    id: 'revision-official',
    title: 'Revision Official',
    description: 'Modern e-commerce website built with cutting-edge web technologies',
    longDescription: 'A comprehensive e-commerce platform featuring product catalog, shopping cart, user authentication, payment processing, and admin dashboard. Built with modern web technologies and optimized for performance.',
    image: '/images/projects/revision-official.jpg',
    images: [
      '/images/projects/revision-official-1.jpg',
      '/images/projects/revision-official-2.jpg',
      '/images/projects/revision-official-3.jpg',
    ],
    techStack: [
      { name: 'Next.js', color: '#000000' },
      { name: 'TypeScript', color: '#3178C6' },
      { name: 'Tailwind CSS', color: '#06B6D4' },
      { name: 'Stripe', color: '#635BFF' },
      { name: 'Vercel', color: '#000000' },
    ],
    links: [
      { type: 'live', url: 'https://revisionofficial.com', label: 'Visit Website' },
    ],
    featured: true,
    category: 'web',
    year: 2024,
    status: 'completed',
  },
  {
    id: 'beincourt-dashboard',
    title: 'BEINCOURT Dashboard',
    description: 'Private company dashboard with advanced analytics and data visualization',
    longDescription: 'A sophisticated business intelligence dashboard for BEINCOURT featuring real-time analytics, data visualization, user management, and reporting capabilities. Built with modern React ecosystem and optimized for enterprise use.',
    image: '/images/projects/beincourt-dashboard.jpg',
    images: [
      '/images/projects/beincourt-dashboard-1.jpg',
      '/images/projects/beincourt-dashboard-2.jpg',
    ],
    techStack: [
      { name: 'React', color: '#61DAFB' },
      { name: 'TypeScript', color: '#3178C6' },
      { name: 'Node.js', color: '#339933' },
      { name: 'PostgreSQL', color: '#336791' },
      { name: 'Chart.js', color: '#FF6384' },
    ],
    links: [
      { type: 'case-study', url: '#', label: 'View Case Study' },
    ],
    featured: true,
    category: 'web',
    year: 2023,
    status: 'completed',
  },
  {
    id: 'web-game-collection',
    title: 'Web Game Collection',
    description: 'Collection of interactive web-based games built with modern JavaScript',
    longDescription: 'A series of engaging web games showcasing advanced JavaScript programming, game physics, and interactive user interfaces. Features include real-time multiplayer capabilities, leaderboards, and responsive design.',
    image: '/images/projects/web-games.jpg',
    techStack: [
      { name: 'JavaScript', color: '#F7DF1E' },
      { name: 'HTML5 Canvas', color: '#E34F26' },
      { name: 'CSS3', color: '#1572B6' },
      { name: 'Socket.io', color: '#010101' },
    ],
    links: [
      { type: 'github', url: '#', label: 'View Source Code' },
    ],
    featured: false,
    category: 'game',
    year: 2023,
    status: 'completed',
  },
  {
    id: 'portfolio-experiments',
    title: 'Portfolio Experiments',
    description: 'Various portfolio designs and interactive experiments',
    longDescription: 'A collection of experimental portfolio designs and interactive web components, exploring different animation techniques, layout approaches, and user experience patterns.',
    image: '/images/projects/portfolio-experiments.jpg',
    techStack: [
      { name: 'React', color: '#61DAFB' },
      { name: 'Framer Motion', color: '#0055FF' },
      { name: 'Three.js', color: '#000000' },
      { name: 'GSAP', color: '#88CE02' },
    ],
    links: [
      { type: 'github', url: '#', label: 'View Experiments' },
    ],
    featured: false,
    category: 'web',
    year: 2022,
    status: 'completed',
  },
];

// Experience Data
export const EXPERIENCES: Experience[] = [
  {
    id: 'cal-poly',
    title: 'Bachelor of Science in Computer Science',
    company: 'California Polytechnic University',
    location: 'California, USA',
    startDate: '2020-09',
    endDate: '2024-06',
    description: [
      'Graduated with a Bachelor of Science degree in Computer Science',
      'Focused on software engineering, algorithms, and data structures',
      'Completed coursework in web development, database systems, and software architecture',
      'Participated in coding competitions and hackathons',
    ],
    technologies: ['Java', 'Python', 'C++', 'JavaScript', 'SQL', 'Git'],
    type: 'education',
    logo: '/images/logos/cal-poly.png',
    website: 'https://www.calpoly.edu/',
  },
  {
    id: 'freelance-developer',
    title: 'Freelance Full-Stack Developer',
    company: 'Self-Employed',
    location: 'Remote',
    startDate: '2023-01',
    description: [
      'Developed custom web applications for various clients',
      'Built e-commerce platforms with payment integration',
      'Created responsive designs and optimized user experiences',
      'Collaborated with clients to understand requirements and deliver solutions',
    ],
    technologies: ['Next.js', 'React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Stripe'],
    type: 'work',
  },
  {
    id: 'beincourt-project',
    title: 'Dashboard Developer',
    company: 'BEINCOURT',
    location: 'Contract',
    startDate: '2023-06',
    endDate: '2023-12',
    description: [
      'Developed a comprehensive business intelligence dashboard',
      'Implemented real-time data visualization and analytics',
      'Built user management and reporting systems',
      'Optimized performance for large datasets',
    ],
    technologies: ['React', 'TypeScript', 'Node.js', 'PostgreSQL', 'Chart.js'],
    type: 'work',
  },
];

// Skills Data
export const SKILLS: SkillCategory[] = [
  {
    name: 'Frontend Development',
    icon: 'Monitor',
    skills: [
      { name: 'React', level: 90, category: 'frontend', color: '#61DAFB', yearsOfExperience: 3 },
      { name: 'Next.js', level: 85, category: 'frontend', color: '#000000', yearsOfExperience: 2 },
      { name: 'TypeScript', level: 88, category: 'frontend', color: '#3178C6', yearsOfExperience: 3 },
      { name: 'JavaScript', level: 92, category: 'frontend', color: '#F7DF1E', yearsOfExperience: 4 },
      { name: 'HTML5', level: 95, category: 'frontend', color: '#E34F26', yearsOfExperience: 5 },
      { name: 'CSS3', level: 90, category: 'frontend', color: '#1572B6', yearsOfExperience: 5 },
      { name: 'Tailwind CSS', level: 85, category: 'frontend', color: '#06B6D4', yearsOfExperience: 2 },
    ],
  },
  {
    name: 'Backend Development',
    icon: 'Server',
    skills: [
      { name: 'Node.js', level: 80, category: 'backend', color: '#339933', yearsOfExperience: 2 },
      { name: 'Python', level: 75, category: 'backend', color: '#3776AB', yearsOfExperience: 3 },
      { name: 'Java', level: 70, category: 'backend', color: '#007396', yearsOfExperience: 2 },
      { name: 'Express.js', level: 78, category: 'backend', color: '#000000', yearsOfExperience: 2 },
      { name: 'REST APIs', level: 82, category: 'backend', color: '#FF6B35', yearsOfExperience: 2 },
    ],
  },
  {
    name: 'Database & Tools',
    icon: 'Database',
    skills: [
      { name: 'PostgreSQL', level: 75, category: 'database', color: '#336791', yearsOfExperience: 2 },
      { name: 'MongoDB', level: 70, category: 'database', color: '#47A248', yearsOfExperience: 1 },
      { name: 'Git', level: 85, category: 'tools', color: '#F05032', yearsOfExperience: 4 },
      { name: 'Docker', level: 65, category: 'tools', color: '#2496ED', yearsOfExperience: 1 },
      { name: 'Vercel', level: 80, category: 'tools', color: '#000000', yearsOfExperience: 2 },
    ],
  },
  {
    name: 'Design & Animation',
    icon: 'Palette',
    skills: [
      { name: 'Figma', level: 75, category: 'design', color: '#F24E1E', yearsOfExperience: 2 },
      { name: 'Framer Motion', level: 80, category: 'frontend', color: '#0055FF', yearsOfExperience: 1 },
      { name: 'GSAP', level: 70, category: 'frontend', color: '#88CE02', yearsOfExperience: 1 },
      { name: 'Three.js', level: 60, category: 'frontend', color: '#000000', yearsOfExperience: 1 },
    ],
  },
];

// Animation Configuration
export const ANIMATION_CONFIG = {
  staggerDelay: 0.1,
  scrollThreshold: 0.2,
  parallaxSpeed: 0.5,
  typewriterSpeed: 50,
  counterDuration: 2000,
} as const;

// Breakpoints (matching Tailwind defaults)
export const BREAKPOINTS = {
  sm: 640,
  md: 768,
  lg: 1024,
  xl: 1280,
  '2xl': 1536,
} as const;

// Contact Information
export const CONTACT_INFO = {
  email: '<EMAIL>', // Update with real email
  phone: '+****************', // Update with real phone
  location: 'California, USA',
  availability: 'Available for freelance work',
} as const;

// SEO Configuration
export const SEO_CONFIG = {
  defaultTitle: 'Tyler Hipolito - Full-Stack Developer',
  titleTemplate: '%s | Tyler Hipolito',
  defaultDescription: 'Full-stack developer and Computer Science graduate from Cal Poly California. Specializing in modern web applications with React, Next.js, and TypeScript.',
  siteUrl: 'https://tylerhipolito.com', // Update with real domain
  defaultImage: '/images/og-image.jpg',
  twitterHandle: '@tylerhipolito', // Update with real handle
} as const;
