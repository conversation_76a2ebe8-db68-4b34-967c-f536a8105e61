'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { ArrowUp, Heart } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useSmoothScroll } from '@/hooks/useScrollAnimation';
import { But<PERSON>, IconButton } from '@/components/ui';
import { PERSONAL_INFO, SOCIAL_LINKS, NAV_ITEMS } from '@/lib/constants';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

// Dynamic icon imports for social links
const iconMap: Record<string, React.ComponentType<{ size?: number; className?: string }>> = {
  Github: require('lucide-react').Github,
  Linkedin: require('lucide-react').Linkedin,
  Mail: require('lucide-react').Mail,
  Twitter: require('lucide-react').Twitter,
};

export const Footer: React.FC = () => {
  const { scrollToTop, scrollToElement } = useSmoothScroll();
  const currentYear = new Date().getFullYear();

  const handleNavClick = (href: string) => {
    const sectionId = href.replace('#', '');
    scrollToElement(sectionId);
  };

  return (
    <motion.footer
      className="bg-muted/30 border-t border-border"
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true, margin: '-100px' }}
      variants={staggerContainer}
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* Brand Section */}
          <motion.div
            className="lg:col-span-2"
            variants={staggerItem}
          >
            <motion.h3
              className="text-2xl font-bold text-foreground mb-4"
              whileHover={{ scale: 1.02 }}
            >
              {PERSONAL_INFO.name}
            </motion.h3>
            <p className="text-muted-foreground mb-6 max-w-md leading-relaxed">
              {PERSONAL_INFO.bio}
            </p>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {SOCIAL_LINKS.map((social, index) => {
                const IconComponent = iconMap[social.icon];
                
                return (
                  <motion.a
                    key={social.platform}
                    href={social.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={cn(
                      'p-2 rounded-lg bg-background border border-border',
                      'hover:bg-accent hover:border-accent-foreground/20',
                      'transition-all duration-300'
                    )}
                    whileHover={{ y: -2, scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                    aria-label={social.label}
                  >
                    {IconComponent && <IconComponent size={20} />}
                  </motion.a>
                );
              })}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div variants={staggerItem}>
            <h4 className="text-lg font-semibold text-foreground mb-4">
              Quick Links
            </h4>
            <nav className="space-y-3">
              {NAV_ITEMS.map((item, index) => (
                <motion.button
                  key={item.href}
                  onClick={() => handleNavClick(item.href)}
                  className={cn(
                    'block text-muted-foreground hover:text-foreground',
                    'transition-colors duration-200 text-left'
                  )}
                  whileHover={{ x: 4 }}
                  initial={{ opacity: 0, x: -10 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  {item.label}
                </motion.button>
              ))}
            </nav>
          </motion.div>

          {/* Contact Info */}
          <motion.div variants={staggerItem}>
            <h4 className="text-lg font-semibold text-foreground mb-4">
              Get In Touch
            </h4>
            <div className="space-y-3">
              <motion.a
                href={`mailto:${PERSONAL_INFO.email}`}
                className="block text-muted-foreground hover:text-foreground transition-colors"
                whileHover={{ x: 4 }}
              >
                {PERSONAL_INFO.email}
              </motion.a>
              <motion.p
                className="text-muted-foreground"
                whileHover={{ x: 4 }}
              >
                {PERSONAL_INFO.location}
              </motion.p>
              
              {/* Resume Download */}
              {PERSONAL_INFO.resume && (
                <motion.a
                  href={PERSONAL_INFO.resume}
                  target="_blank"
                  rel="noopener noreferrer"
                  className={cn(
                    'inline-flex items-center gap-2 mt-4 px-4 py-2',
                    'bg-primary text-white rounded-lg',
                    'hover:bg-primary/90 transition-colors'
                  )}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  Download Resume
                </motion.a>
              )}
            </div>
          </motion.div>
        </div>

        {/* Bottom Section */}
        <motion.div
          className="mt-12 pt-8 border-t border-border flex flex-col sm:flex-row items-center justify-between gap-4"
          variants={fadeInUp}
        >
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <span>© {currentYear} {PERSONAL_INFO.name}. Made with</span>
            <motion.div
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 1, repeat: Infinity, repeatDelay: 2 }}
            >
              <Heart size={16} className="text-red-500 fill-current" />
            </motion.div>
            <span>and lots of coffee.</span>
          </div>

          {/* Back to Top Button */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <IconButton
              variant="ghost"
              size="sm"
              onClick={scrollToTop}
              icon={<ArrowUp size={16} />}
              className="text-muted-foreground hover:text-foreground"
            >
              Back to Top
            </IconButton>
          </motion.div>
        </motion.div>
      </div>

      {/* Decorative Elements */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        <motion.div
          className="absolute -top-24 -right-24 w-48 h-48 bg-primary/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
          }}
        />
        <motion.div
          className="absolute -bottom-24 -left-24 w-48 h-48 bg-secondary/5 rounded-full blur-3xl"
          animate={{
            scale: [1.2, 1, 1.2],
            opacity: [0.5, 0.3, 0.5],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: 'easeInOut',
            delay: 4,
          }}
        />
      </div>
    </motion.footer>
  );
};

export default Footer;
