'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { GraduationCap, Code, Coffee, Lightbulb, Heart, MapPin } from 'lucide-react';
import { Card, CardContent } from '@/components/ui';
import { useScrollAnimation } from '@/hooks/useScrollAnimation';
import { PERSONAL_INFO, EXPERIENCES } from '@/lib/constants';
import { fadeInUp, staggerContainer, staggerItem } from '@/lib/animations';

const highlights = [
  {
    icon: GraduationCap,
    title: 'Cal Poly Education',
    description: 'Computer Science degree from California Polytechnic University',
    color: '#3b82f6',
  },
  {
    icon: Code,
    title: 'Full-Stack Development',
    description: 'Expertise in modern web technologies and frameworks',
    color: '#10b981',
  },
  {
    icon: Lightbulb,
    title: 'Problem Solver',
    description: 'Passionate about creating elegant solutions to complex challenges',
    color: '#f59e0b',
  },
  {
    icon: Heart,
    title: 'User-Focused',
    description: 'Dedicated to building applications that users love',
    color: '#ef4444',
  },
];

const personalStats = [
  { label: 'Years of Experience', value: '3+', suffix: '' },
  { label: 'Projects Completed', value: '15', suffix: '+' },
  { label: 'Technologies Mastered', value: '20', suffix: '+' },
  { label: 'Coffee Cups', value: '∞', suffix: '' },
];

export const About: React.FC = () => {
  const { ref, isVisible } = useScrollAnimation({ threshold: 0.1 });

  // Get Cal Poly education info
  const education = EXPERIENCES.find(exp => exp.type === 'education');

  return (
    <section 
      id="about" 
      ref={ref}
      className="py-20 lg:py-32 bg-gradient-to-b from-background to-muted/30"
    >
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={staggerContainer}
          initial="hidden"
          animate={isVisible ? "visible" : "hidden"}
          className="max-w-6xl mx-auto"
        >
          {/* Section Header */}
          <motion.div 
            variants={staggerItem}
            className="text-center mb-16"
          >
            <motion.h2 
              className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-6"
              variants={fadeInUp}
            >
              About Me
            </motion.h2>
            <motion.p 
              className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed"
              variants={fadeInUp}
            >
              A passionate developer with a strong foundation in computer science and 
              a love for creating innovative web solutions.
            </motion.p>
          </motion.div>

          {/* Main Content Grid */}
          <div className="grid lg:grid-cols-2 gap-12 lg:gap-16 items-center mb-20">
            {/* Left Column - Story */}
            <motion.div variants={staggerItem} className="space-y-6">
              <motion.h3 
                className="text-2xl md:text-3xl font-semibold text-foreground mb-6"
                variants={fadeInUp}
              >
                My Journey
              </motion.h3>
              
              <motion.div variants={fadeInUp} className="space-y-4">
                <p className="text-lg text-muted-foreground leading-relaxed">
                  My passion for technology began during my studies at{' '}
                  <span className="text-foreground font-semibold">
                    California Polytechnic University
                  </span>, where I earned my Computer Science degree. The hands-on, 
                  learn-by-doing approach at Cal Poly shaped my problem-solving mindset 
                  and technical foundation.
                </p>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  I specialize in full-stack web development, with expertise in modern 
                  frameworks like React and Next.js. I'm passionate about creating 
                  applications that not only function flawlessly but also provide 
                  exceptional user experiences.
                </p>
                
                <p className="text-lg text-muted-foreground leading-relaxed">
                  When I'm not coding, you'll find me exploring new technologies, 
                  contributing to open-source projects, or enjoying a good cup of coffee 
                  while planning my next project.
                </p>
              </motion.div>

              {/* Location */}
              <motion.div 
                variants={fadeInUp}
                className="flex items-center gap-2 text-muted-foreground"
              >
                <MapPin size={20} className="text-primary" />
                <span>{PERSONAL_INFO.location}</span>
              </motion.div>
            </motion.div>

            {/* Right Column - Education Card */}
            <motion.div variants={staggerItem}>
              {education && (
                <Card className="p-8 bg-gradient-to-br from-primary/5 to-primary/10 border-primary/20">
                  <CardContent className="space-y-6">
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-primary/10 rounded-lg">
                        <GraduationCap size={32} className="text-primary" />
                      </div>
                      <div>
                        <h4 className="text-xl font-semibold text-foreground">
                          {education.title}
                        </h4>
                        <p className="text-primary font-medium">
                          {education.company}
                        </p>
                      </div>
                    </div>
                    
                    <div className="space-y-3">
                      {education.description.map((item, index) => (
                        <motion.div
                          key={index}
                          initial={{ opacity: 0, x: -20 }}
                          animate={isVisible ? { opacity: 1, x: 0 } : {}}
                          transition={{ delay: 0.5 + index * 0.1 }}
                          className="flex items-start gap-3"
                        >
                          <div className="w-2 h-2 bg-primary rounded-full mt-2 flex-shrink-0" />
                          <p className="text-muted-foreground">{item}</p>
                        </motion.div>
                      ))}
                    </div>

                    {/* Technologies from education */}
                    <div className="flex flex-wrap gap-2 pt-4">
                      {education.technologies.slice(0, 4).map((tech, index) => (
                        <motion.span
                          key={tech}
                          initial={{ opacity: 0, scale: 0.8 }}
                          animate={isVisible ? { opacity: 1, scale: 1 } : {}}
                          transition={{ delay: 0.8 + index * 0.1 }}
                          className="px-3 py-1 bg-primary/10 text-primary text-sm rounded-full font-medium"
                        >
                          {tech}
                        </motion.span>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          </div>

          {/* Highlights Grid */}
          <motion.div variants={staggerItem} className="mb-20">
            <motion.h3 
              className="text-2xl md:text-3xl font-semibold text-foreground text-center mb-12"
              variants={fadeInUp}
            >
              What Drives Me
            </motion.h3>
            
            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              {highlights.map((highlight, index) => {
                const IconComponent = highlight.icon;
                
                return (
                  <motion.div
                    key={highlight.title}
                    variants={fadeInUp}
                    whileHover={{ y: -5, scale: 1.02 }}
                    className="group"
                  >
                    <Card className="p-6 text-center h-full hover:shadow-lg transition-all duration-300">
                      <CardContent className="space-y-4">
                        <div 
                          className="w-16 h-16 mx-auto rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform duration-300"
                          style={{ backgroundColor: `${highlight.color}15` }}
                        >
                          <IconComponent 
                            size={32} 
                            style={{ color: highlight.color }}
                          />
                        </div>
                        <h4 className="text-lg font-semibold text-foreground">
                          {highlight.title}
                        </h4>
                        <p className="text-muted-foreground text-sm leading-relaxed">
                          {highlight.description}
                        </p>
                      </CardContent>
                    </Card>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Personal Stats */}
          <motion.div variants={staggerItem}>
            <motion.h3 
              className="text-2xl md:text-3xl font-semibold text-foreground text-center mb-12"
              variants={fadeInUp}
            >
              By the Numbers
            </motion.h3>
            
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
              {personalStats.map((stat, index) => (
                <motion.div
                  key={stat.label}
                  variants={fadeInUp}
                  whileHover={{ scale: 1.05 }}
                  className="text-center group"
                >
                  <motion.div
                    initial={{ scale: 0 }}
                    animate={isVisible ? { scale: 1 } : {}}
                    transition={{ 
                      delay: 0.5 + index * 0.1,
                      type: "spring",
                      stiffness: 200,
                      damping: 20
                    }}
                    className="text-4xl md:text-5xl font-bold text-primary mb-2 group-hover:text-primary/80 transition-colors"
                  >
                    {stat.value}{stat.suffix}
                  </motion.div>
                  <p className="text-muted-foreground font-medium">
                    {stat.label}
                  </p>
                </motion.div>
              ))}
            </div>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
